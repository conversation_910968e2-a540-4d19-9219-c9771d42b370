from flask import Flask
from flask_cors import CORS
from app.config import config
from app.extensions import db, jwt, migrate

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    migrate.init_app(app, db)
    CORS(app)

    # 导入模型（确保模型被注册）
    from app.models import User, Student

    # 注册蓝图
    from app.api import api_bp
    app.register_blueprint(api_bp)

    return app

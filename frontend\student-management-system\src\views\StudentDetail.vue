<template>
  <div class="student-detail">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px">
        <div class="sidebar">
          <div class="logo">
            <h3>学生管理系统</h3>
          </div>
          <el-menu
            :default-active="'/students'"
            router
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
          >
            <el-menu-item index="/dashboard">
              <el-icon><House /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="/students">
              <el-icon><User /></el-icon>
              <span>学生管理</span>
            </el-menu-item>
            <el-menu-item v-if="authStore.isAdmin" index="/students/create">
              <el-icon><Plus /></el-icon>
              <span>添加学生</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header>
          <div class="header">
            <div class="breadcrumb">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item>首页</el-breadcrumb-item>
                <el-breadcrumb-item>学生管理</el-breadcrumb-item>
                <el-breadcrumb-item>学生详情</el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div class="user-info">
              <el-dropdown @command="handleCommand">
                <span class="user-name">
                  {{ authStore.user?.username }}
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>
        
        <!-- 主要内容 -->
        <el-main>
          <div class="student-content">
            <el-card v-loading="studentStore.loading" class="detail-card">
              <template #header>
                <div class="card-header">
                  <div class="header-title">
                    <el-tag type="success" effect="dark" round>详情</el-tag>
                    <span class="student-name">{{ studentStore.currentStudent?.name || '学生信息' }}</span>
                  </div>
                  <div class="header-buttons">
                    <el-button @click="$router.push('/students')">
                      <el-icon><Back /></el-icon>
                      返回列表
                    </el-button>
                    <el-button
                      v-if="authStore.isAdmin"
                      type="primary"
                      @click="editStudent"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑资料
                    </el-button>
                    <el-button
                      v-if="authStore.isAdmin"
                      type="danger"
                      @click="confirmDelete"
                    >
                      <el-icon><Delete /></el-icon>
                      删除学生
                    </el-button>
                  </div>
                </div>
              </template>
              
              <div v-if="studentStore.currentStudent" class="student-info">
                <!-- 学生基本信息卡片 -->
                <el-row :gutter="20">
                  <el-col :span="8">
                    <div class="info-card">
                      <div class="student-avatar">
                        <el-avatar 
                          :size="100" 
                          :icon="User"
                          :src="avatarUrl"
                        />
                      </div>
                      <h3>{{ studentStore.currentStudent.name }}</h3>
                      <div class="student-id-tag">
                        <el-tag effect="plain">学号: {{ studentStore.currentStudent.student_id }}</el-tag>
                      </div>
                      <div class="student-basic">
                        <p><el-icon><UserFilled /></el-icon> {{ studentStore.currentStudent.gender }}</p>
                        <p><el-icon><Calendar /></el-icon> {{ studentStore.currentStudent.age }}岁</p>
                        <p><el-icon><School /></el-icon> {{ studentStore.currentStudent.grade }} {{ studentStore.currentStudent.class }}</p>
                      </div>
                    </div>
                  </el-col>
                  
                  <el-col :span="16">
                    <el-tabs v-model="activeTab" class="detail-tabs">
                      <el-tab-pane label="详细信息" name="details">
                        <el-descriptions :column="2" border size="large">
                          <el-descriptions-item label="学号">
                            <el-tag type="info">{{ studentStore.currentStudent.student_id }}</el-tag>
                          </el-descriptions-item>
                          <el-descriptions-item label="姓名">
                            {{ studentStore.currentStudent.name }}
                          </el-descriptions-item>
                          <el-descriptions-item label="性别">
                            <el-tag :type="studentStore.currentStudent.gender === '男' ? 'primary' : 'success'">
                              {{ studentStore.currentStudent.gender }}
                            </el-tag>
                          </el-descriptions-item>
                          <el-descriptions-item label="年龄">
                            {{ studentStore.currentStudent.age }}岁
                          </el-descriptions-item>
                          <el-descriptions-item label="年级">
                            {{ studentStore.currentStudent.grade }}
                          </el-descriptions-item>
                          <el-descriptions-item label="班级">
                            {{ studentStore.currentStudent.class }}
                          </el-descriptions-item>
                          <el-descriptions-item label="创建时间" :span="2">
                            <el-icon><Timer /></el-icon> {{ formatDate(studentStore.currentStudent.created_at) }}
                          </el-descriptions-item>
                          <el-descriptions-item label="更新时间" :span="2">
                            <el-icon><Edit /></el-icon> {{ formatDate(studentStore.currentStudent.updated_at) }}
                          </el-descriptions-item>
                        </el-descriptions>
                      </el-tab-pane>
                      
                      <el-tab-pane label="统计信息" name="statistics">
                        <div class="statistics-content">
                          <el-row :gutter="20">
                            <el-col :span="12">
                              <div class="statistic-card">
                                <h4>年级分布</h4>
                                <div class="statistic-chart grade-chart">
                                  <div class="chart-placeholder">
                                    <div class="grade-indicator">
                                      <div class="grade-label">{{ studentStore.currentStudent.grade }}</div>
                                      <div class="grade-bar">
                                        <div class="grade-progress"></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </el-col>
                            
                            <el-col :span="12">
                              <div class="statistic-card">
                                <h4>年龄统计</h4>
                                <div class="statistic-chart age-chart">
                                  <div class="chart-placeholder">
                                    <el-progress 
                                      type="dashboard"
                                      :percentage="(studentStore.currentStudent.age / 30) * 100"
                                      :format="() => studentStore.currentStudent.age + '岁'"
                                      :stroke-width="10"
                                    />
                                  </div>
                                </div>
                              </div>
                            </el-col>
                          </el-row>
                        </div>
                      </el-tab-pane>
                      
                      <el-tab-pane label="操作记录" name="logs">
                        <el-timeline>
                          <el-timeline-item
                            timestamp={formatDate(studentStore.currentStudent.updated_at)}
                            type="primary"
                          >
                            <h4>最近更新</h4>
                            <p>学生信息被更新</p>
                          </el-timeline-item>
                          <el-timeline-item
                            timestamp={formatDate(studentStore.currentStudent.created_at)}
                            type="success"
                          >
                            <h4>创建记录</h4>
                            <p>学生信息被创建</p>
                          </el-timeline-item>
                        </el-timeline>
                      </el-tab-pane>
                    </el-tabs>
                  </el-col>
                </el-row>
              </div>
              
              <div v-else-if="!studentStore.loading" class="no-data">
                <el-result
                  icon="error"
                  title="未找到学生信息"
                  sub-title="该学生可能已被删除或不存在"
                >
                  <template #extra>
                    <el-button type="primary" @click="$router.push('/students')">返回学生列表</el-button>
                  </template>
                </el-result>
              </div>
            </el-card>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { House, User, Plus, ArrowDown, Back, Edit, Delete, UserFilled, Calendar, School, Timer } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useStudentStore } from '@/stores/student'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const studentStore = useStudentStore()
const activeTab = ref('details')

// 根据学生姓名生成头像URL
const avatarUrl = computed(() => {
  if (!studentStore.currentStudent) return ''
  // 使用Multiavatar API生成头像
  const name = encodeURIComponent(studentStore.currentStudent.name)
  return `https://api.multiavatar.com/${name}.svg`
})

onMounted(async () => {
  authStore.initAuth()
  await fetchStudent()
})

const fetchStudent = async () => {
  try {
    const studentId = route.params.id
    await studentStore.fetchStudent(studentId)
    if (!studentStore.currentStudent) {
      throw new Error('学生不存在')
    }
  } catch (error) {
    ElMessage.error('获取学生信息失败：' + (error.message || '未知错误'))
  }
}

const editStudent = () => {
  router.push(`/students/${route.params.id}/edit`)
}

const confirmDelete = async () => {
  if (!studentStore.currentStudent) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除学生 ${studentStore.currentStudent.name} (学号: ${studentStore.currentStudent.student_id}) 吗？此操作不可逆！`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    await studentStore.deleteStudent(route.params.id)
    ElMessage.success(`学生 ${studentStore.currentStudent.name} 已成功删除`)
    router.push('/students')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + (error.message || '未知错误'))
    }
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '暂无数据'
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      authStore.logout()
      ElMessage.success('已退出登录')
      router.push('/login')
    } catch {
      // 用户取消
    }
  }
}
</script>

<style scoped>
.student-detail {
  height: 100vh;
}

.sidebar {
  height: 100vh;
  background-color: #304156;
}

.logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #434a50;
}

.logo h3 {
  color: #bfcbd9;
  margin: 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.user-name {
  cursor: pointer;
  color: #606266;
  display: flex;
  align-items: center;
}

.student-content {
  padding: 20px;
}

.detail-card {
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.student-name {
  font-size: 18px;
  font-weight: bold;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.student-info {
  padding: 20px 0;
}

.info-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  height: 100%;
}

.student-avatar {
  margin-bottom: 15px;
}

.student-id-tag {
  margin: 10px 0;
}

.student-basic {
  margin-top: 20px;
  text-align: left;
}

.student-basic p {
  margin: 10px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-tabs {
  height: 100%;
}

.statistics-content {
  padding: 20px 0;
}

.statistic-card {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 200px;
  margin-bottom: 20px;
}

.statistic-card h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #606266;
}

.statistic-chart {
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.grade-indicator {
  width: 100%;
}

.grade-label {
  margin-bottom: 10px;
  font-weight: bold;
}

.grade-bar {
  height: 20px;
  background: #e4e7ed;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.grade-progress {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: #409EFF;
  width: 75%;
  border-radius: 10px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}
</style>

import { defineStore } from 'pinia'
import { studentAPI } from '@/api'

export const useStudentStore = defineStore('student', {
  state: () => ({
    students: [],
    currentStudent: null,
    loading: false,
    total: 0
  }),

  actions: {
    // 获取学生列表
    async fetchStudents() {
      this.loading = true
      try {
        const response = await studentAPI.getStudents()
        this.students = response.students
        this.total = response.total
        return response
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取单个学生信息
    async fetchStudent(id) {
      this.loading = true
      try {
        const response = await studentAPI.getStudent(id)
        this.currentStudent = response.student
        return response
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    // 创建学生
    async createStudent(studentData) {
      try {
        const response = await studentAPI.createStudent(studentData)
        this.students.push(response.student)
        this.total += 1
        return response
      } catch (error) {
        throw error
      }
    },

    // 更新学生信息
    async updateStudent(id, studentData) {
      try {
        const response = await studentAPI.updateStudent(id, studentData)
        const index = this.students.findIndex(s => s.id === id)
        if (index !== -1) {
          this.students[index] = response.student
        }
        this.currentStudent = response.student
        return response
      } catch (error) {
        throw error
      }
    },

    // 删除学生
    async deleteStudent(id) {
      try {
        const response = await studentAPI.deleteStudent(id)
        this.students = this.students.filter(s => s.id !== id)
        this.total -= 1
        return response
      } catch (error) {
        throw error
      }
    }
  }
})

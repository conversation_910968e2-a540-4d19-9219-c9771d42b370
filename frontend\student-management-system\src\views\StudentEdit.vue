<template>
  <div class="student-edit">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px">
        <div class="sidebar">
          <div class="logo">
            <h3>学生管理系统</h3>
          </div>
          <el-menu
            :default-active="'/students'"
            router
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
          >
            <el-menu-item index="/dashboard">
              <el-icon><House /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="/students">
              <el-icon><User /></el-icon>
              <span>学生管理</span>
            </el-menu-item>
            <el-menu-item v-if="authStore.isAdmin" index="/students/create">
              <el-icon><Plus /></el-icon>
              <span>添加学生</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header>
          <div class="header">
            <div class="breadcrumb">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item>首页</el-breadcrumb-item>
                <el-breadcrumb-item>学生管理</el-breadcrumb-item>
                <el-breadcrumb-item>编辑学生</el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div class="user-info">
              <el-dropdown @command="handleCommand">
                <span class="user-name">
                  {{ authStore.user?.username }}
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>
        
        <!-- 主要内容 -->
        <el-main>
          <div class="student-content">
            <el-card v-loading="studentStore.loading" class="form-card">
              <template #header>
                <div class="card-header">
                  <span>
                    <el-tag type="warning" effect="dark" round>编辑</el-tag>
                    学生: {{ studentStore.currentStudent?.name || '加载中...' }}
                  </span>
                  <div class="header-buttons">
                    <el-button @click="$router.push(`/students/${route.params.id}`)" type="info" plain>
                      <el-icon><View /></el-icon>
                      查看详情
                    </el-button>
                    <el-button @click="$router.push('/students')" type="primary" plain>
                      <el-icon><ArrowLeft /></el-icon>
                      返回列表
                    </el-button>
                  </div>
                </div>
              </template>
              
              <div class="form-container" v-if="!studentStore.loading && studentForm.name">
                <el-alert
                  title="请修改以下信息，保存后数据将立即更新"
                  type="info"
                  show-icon
                  :closable="false"
                  class="edit-alert"
                />

                <el-tabs v-model="activeTab" class="edit-tabs">
                  <el-tab-pane label="基本信息" name="basic">
                    <el-form
                      ref="studentFormRef"
                      :model="studentForm"
                      :rules="studentRules"
                      label-width="100px"
                      class="student-form"
                      status-icon
                    >
                      <el-row :gutter="20">
                        <el-col :span="12">
                          <el-form-item label="学号" prop="student_id">
                            <el-input
                              v-model="studentForm.student_id"
                              placeholder="请输入学号"
                              clearable
                              maxlength="20"
                              show-word-limit
                              :prefix-icon="Ticket"
                            />
                            <div class="form-tip">* 学号为唯一标识，请谨慎修改</div>
                          </el-form-item>

                          <el-form-item label="姓名" prop="name">
                            <el-input
                              v-model="studentForm.name"
                              placeholder="请输入姓名"
                              clearable
                              maxlength="20"
                              show-word-limit
                              :prefix-icon="User"
                            />
                          </el-form-item>
                          
                          <el-form-item label="性别" prop="gender">
                            <el-radio-group v-model="studentForm.gender">
                              <el-radio label="男">男</el-radio>
                              <el-radio label="女">女</el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </el-col>
                        
                        <el-col :span="12">
                          <el-form-item label="年龄" prop="age">
                            <el-input-number
                              v-model="studentForm.age"
                              :min="6"
                              :max="30"
                              placeholder="请输入年龄"
                              style="width: 100%"
                              controls-position="right"
                            />
                          </el-form-item>
                          
                          <el-form-item label="年级" prop="grade">
                            <el-select v-model="studentForm.grade" placeholder="请选择年级" style="width: 100%">
                              <el-option v-for="grade in gradeOptions" :key="grade" :label="grade" :value="grade" />
                            </el-select>
                          </el-form-item>
                          
                          <el-form-item label="班级" prop="class">
                            <el-input
                              v-model="studentForm.class"
                              placeholder="请输入班级"
                              clearable
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>
                  </el-tab-pane>

                  <el-tab-pane label="数据预览" name="preview">
                    <el-descriptions title="当前学生信息" :column="2" border>
                      <el-descriptions-item label="学号">{{ studentStore.currentStudent?.student_id }}</el-descriptions-item>
                      <el-descriptions-item label="姓名">{{ studentStore.currentStudent?.name }}</el-descriptions-item>
                      <el-descriptions-item label="性别">{{ studentStore.currentStudent?.gender }}</el-descriptions-item>
                      <el-descriptions-item label="年龄">{{ studentStore.currentStudent?.age }}</el-descriptions-item>
                      <el-descriptions-item label="年级">{{ studentStore.currentStudent?.grade }}</el-descriptions-item>
                      <el-descriptions-item label="班级">{{ studentStore.currentStudent?.class }}</el-descriptions-item>
                      <el-descriptions-item label="创建时间" :span="2">{{ formatDate(studentStore.currentStudent?.created_at) }}</el-descriptions-item>
                      <el-descriptions-item label="更新时间" :span="2">{{ formatDate(studentStore.currentStudent?.updated_at) }}</el-descriptions-item>
                    </el-descriptions>

                    <div class="changes-preview">
                      <h4>修改预览</h4>
                      <el-divider />
                      <el-tag v-if="studentForm.student_id !== studentStore.currentStudent?.student_id" type="warning">
                        学号: {{ studentStore.currentStudent?.student_id }} -> {{ studentForm.student_id }}
                      </el-tag>
                      <el-tag v-if="studentForm.name !== studentStore.currentStudent?.name" type="warning">
                        姓名: {{ studentStore.currentStudent?.name }} -> {{ studentForm.name }}
                      </el-tag>
                      <el-tag v-if="studentForm.gender !== studentStore.currentStudent?.gender" type="warning">
                        性别: {{ studentStore.currentStudent?.gender }} -> {{ studentForm.gender }}
                      </el-tag>
                      <el-tag v-if="studentForm.age !== studentStore.currentStudent?.age" type="warning">
                        年龄: {{ studentStore.currentStudent?.age }} -> {{ studentForm.age }}
                      </el-tag>
                      <el-tag v-if="studentForm.grade !== studentStore.currentStudent?.grade" type="warning">
                        年级: {{ studentStore.currentStudent?.grade }} -> {{ studentForm.grade }}
                      </el-tag>
                      <el-tag v-if="studentForm.class !== studentStore.currentStudent?.class" type="warning">
                        班级: {{ studentStore.currentStudent?.class }} -> {{ studentForm.class }}
                      </el-tag>
                      <p v-if="!hasChanges">暂无修改</p>
                    </div>
                  </el-tab-pane>
                </el-tabs>

                <div class="form-actions">
                  <el-button type="primary" :loading="loading" @click="handleSubmit">
                    <el-icon><Check /></el-icon> 保存修改
                  </el-button>
                  <el-button @click="resetForm">
                    <el-icon><RefreshRight /></el-icon> 重置表单
                  </el-button>
                  <el-button type="danger" @click="confirmCancel">
                    <el-icon><Close /></el-icon> 取消编辑
                  </el-button>
                </div>
              </div>
              
              <div v-else-if="!studentStore.loading && !studentForm.name" class="error-container">
                <el-result
                  icon="error"
                  title="获取学生信息失败"
                  sub-title="无法加载该学生的信息，请返回学生列表重试"
                >
                  <template #extra>
                    <el-button type="primary" @click="$router.push('/students')">返回学生列表</el-button>
                  </template>
                </el-result>
              </div>
            </el-card>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { House, User, Plus, ArrowDown, ArrowLeft, View, Check, RefreshRight, Close, Ticket } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useStudentStore } from '@/stores/student'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const studentStore = useStudentStore()

const studentFormRef = ref()
const loading = ref(false)
const activeTab = ref('basic')

// 年级选项
const gradeOptions = [
  '一年级', '二年级', '三年级', '四年级', '五年级', '六年级',
  '初一', '初二', '初三',
  '高一', '高二', '高三',
  '大一', '大二', '大三', '大四'
]

const studentForm = reactive({
  student_id: '',
  name: '',
  gender: '',
  age: null,
  grade: '',
  class: ''
})

// 检查是否有修改
const hasChanges = computed(() => {
  if (!studentStore.currentStudent) return false
  
  return (
    studentForm.student_id !== studentStore.currentStudent.student_id ||
    studentForm.name !== studentStore.currentStudent.name ||
    studentForm.gender !== studentStore.currentStudent.gender ||
    studentForm.age !== studentStore.currentStudent.age ||
    studentForm.grade !== studentStore.currentStudent.grade ||
    studentForm.class !== studentStore.currentStudent.class
  )
})

const studentRules = {
  student_id: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    { min: 5, max: 20, message: '学号长度应为5-20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '学号只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应为2-20个字符', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    { type: 'number', min: 6, max: 30, message: '年龄必须在6-30岁之间', trigger: 'blur' }
  ],
  grade: [
    { required: true, message: '请选择年级', trigger: 'change' }
  ],
  class: [
    { required: true, message: '请输入班级', trigger: 'blur' },
    { min: 1, max: 20, message: '班级长度应为1-20个字符', trigger: 'blur' }
  ]
}

onMounted(async () => {
  authStore.initAuth()
  if (!authStore.isAdmin) {
    ElMessage.warning('您没有编辑权限，即将返回学生列表')
    router.push('/students')
    return
  }
  await fetchStudent()
})

const fetchStudent = async () => {
  try {
    const studentId = route.params.id
    await studentStore.fetchStudent(studentId)
    if (!studentStore.currentStudent) {
      throw new Error('学生不存在')
    }
  } catch (error) {
    ElMessage.error('获取学生信息失败：' + (error.message || '未知错误'))
  }
}

// 监听学生数据变化，填充表单
watch(
  () => studentStore.currentStudent,
  (student) => {
    if (student) {
      Object.assign(studentForm, {
        student_id: student.student_id,
        name: student.name,
        gender: student.gender,
        age: student.age,
        grade: student.grade,
        class: student.class
      })
    }
  },
  { immediate: true }
)

const handleSubmit = async () => {
  if (!studentFormRef.value) return
  
  await studentFormRef.value.validate(async (valid) => {
    if (valid) {
      if (!hasChanges.value) {
        ElMessage.info('没有修改任何内容')
        return
      }

      loading.value = true
      try {
        const studentId = route.params.id
        await studentStore.updateStudent(studentId, studentForm)
        ElMessage.success({
          message: '学生信息更新成功！',
          duration: 2000
        })
        setTimeout(() => {
          router.push(`/students/${studentId}`)
        }, 1000)
      } catch (error) {
        ElMessage.error({
          message: error.message || '更新失败，请检查学号是否重复',
          duration: 5000
        })
      } finally {
        loading.value = false
      }
    }
  })
}

const resetForm = () => {
  if (studentStore.currentStudent) {
    Object.assign(studentForm, {
      student_id: studentStore.currentStudent.student_id,
      name: studentStore.currentStudent.name,
      gender: studentStore.currentStudent.gender,
      age: studentStore.currentStudent.age,
      grade: studentStore.currentStudent.grade,
      class: studentStore.currentStudent.class
    })
    ElMessage.info('表单已重置')
  }
}

const confirmCancel = async () => {
  if (!hasChanges.value) {
    router.push(`/students/${route.params.id}`)
    return
  }

  try {
    await ElMessageBox.confirm('您有未保存的修改，确定要取消编辑吗？', '确认提示', {
      confirmButtonText: '确定',
      cancelButtonText: '继续编辑',
      type: 'warning'
    })
    router.push(`/students/${route.params.id}`)
  } catch {
    // 用户取消
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '暂无数据'
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      authStore.logout()
      ElMessage.success('已退出登录')
      router.push('/login')
    } catch {
      // 用户取消
    }
  }
}
</script>

<style scoped>
.student-edit {
  height: 100vh;
}

.sidebar {
  height: 100vh;
  background-color: #304156;
}

.logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #434a50;
}

.logo h3 {
  color: #bfcbd9;
  margin: 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.user-name {
  cursor: pointer;
  color: #606266;
  display: flex;
  align-items: center;
}

.student-content {
  padding: 20px;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.form-container {
  padding: 10px;
}

.edit-alert {
  margin-bottom: 20px;
}

.edit-tabs {
  margin-top: 20px;
}

.student-form {
  margin-top: 20px;
}

.form-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.changes-preview {
  margin-top: 30px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.changes-preview h4 {
  margin-top: 0;
  color: #606266;
}

.changes-preview .el-tag {
  margin: 5px;
}

.error-container {
  padding: 40px 0;
}
</style>

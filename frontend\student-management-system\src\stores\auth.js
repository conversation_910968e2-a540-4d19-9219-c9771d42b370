import { defineStore } from 'pinia'
import { authAPI } from '@/api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isAuthenticated: false
  }),

  getters: {
    isAdmin: (state) => state.user?.role === 'admin'
  },

  actions: {
    // 初始化认证状态
    initAuth() {
      const token = localStorage.getItem('access_token')
      const user = localStorage.getItem('user')
      
      if (token && user) {
        this.token = token
        this.user = JSON.parse(user)
        this.isAuthenticated = true
      }
    },

    // 登录
    async login(credentials) {
      try {
        const response = await authAPI.login(credentials)
        
        this.token = response.access_token
        this.user = response.user
        this.isAuthenticated = true
        
        // 保存到localStorage
        localStorage.setItem('access_token', response.access_token)
        localStorage.setItem('user', JSON.stringify(response.user))
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 注册
    async register(userData) {
      try {
        const response = await authAPI.register(userData)
        return response
      } catch (error) {
        throw error
      }
    },

    // 登出
    logout() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      
      localStorage.removeItem('access_token')
      localStorage.removeItem('user')
    },

    // 获取当前用户信息
    async fetchUser() {
      try {
        const response = await authAPI.getMe()
        this.user = response.user
        localStorage.setItem('user', JSON.stringify(response.user))
        return response
      } catch (error) {
        this.logout()
        throw error
      }
    }
  }
})

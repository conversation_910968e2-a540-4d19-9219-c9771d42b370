from flask import request
from flask_restful import Resource
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.student import Student
from app.models.user import User
from app.extensions import db

class StudentListResource(Resource):
    @jwt_required()
    def get(self):
        """获取学生列表"""
        students = Student.query.all()
        return {
            'students': [student.to_dict() for student in students],
            'total': len(students)
        }, 200
    
    @jwt_required()
    def post(self):
        """创建学生记录"""
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        # 检查权限（只有管理员可以创建学生记录）
        if user.role != 'admin':
            return {'message': '权限不足'}, 403
        
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['name', 'gender', 'age', 'grade', 'class', 'student_id']
        if not all(k in data for k in required_fields):
            return {'message': '缺少必需字段'}, 400
        
        # 检查学号是否已存在
        if Student.query.filter_by(student_id=data['student_id']).first():
            return {'message': '学号已存在'}, 400
        
        # 创建学生记录
        student = Student(
            name=data['name'],
            gender=data['gender'],
            age=data['age'],
            grade=data['grade'],
            class_name=data['class'],
            student_id=data['student_id']
        )
        
        try:
            db.session.add(student)
            db.session.commit()
            return {'message': '学生记录创建成功', 'student': student.to_dict()}, 201
        except Exception as e:
            db.session.rollback()
            return {'message': '创建失败'}, 500

class StudentResource(Resource):
    @jwt_required()
    def get(self, student_id):
        """获取单个学生信息"""
        student = Student.query.get(student_id)
        if student:
            return {'student': student.to_dict()}, 200
        return {'message': '学生不存在'}, 404
    
    @jwt_required()
    def put(self, student_id):
        """更新学生信息"""
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        # 检查权限
        if user.role != 'admin':
            return {'message': '权限不足'}, 403
        
        student = Student.query.get(student_id)
        if not student:
            return {'message': '学生不存在'}, 404
        
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            student.name = data['name']
        if 'gender' in data:
            student.gender = data['gender']
        if 'age' in data:
            student.age = data['age']
        if 'grade' in data:
            student.grade = data['grade']
        if 'class' in data:
            student.class_name = data['class']
        if 'student_id' in data:
            # 检查新学号是否已存在
            existing = Student.query.filter_by(student_id=data['student_id']).first()
            if existing and existing.id != student_id:
                return {'message': '学号已存在'}, 400
            student.student_id = data['student_id']
        
        try:
            db.session.commit()
            return {'message': '更新成功', 'student': student.to_dict()}, 200
        except Exception as e:
            db.session.rollback()
            return {'message': '更新失败'}, 500
    
    @jwt_required()
    def delete(self, student_id):
        """删除学生记录"""
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        # 检查权限
        if user.role != 'admin':
            return {'message': '权限不足'}, 403
        
        student = Student.query.get(student_id)
        if not student:
            return {'message': '学生不存在'}, 404
        
        try:
            db.session.delete(student)
            db.session.commit()
            return {'message': '删除成功'}, 200
        except Exception as e:
            db.session.rollback()
            return {'message': '删除失败'}, 500

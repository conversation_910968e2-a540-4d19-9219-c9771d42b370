<template>
  <div class="student-list">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px">
        <div class="sidebar">
          <div class="logo">
            <h3>学生管理系统</h3>
          </div>
          <el-menu
            :default-active="$route.path"
            router
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
          >
            <el-menu-item index="/dashboard">
              <el-icon><House /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="/students">
              <el-icon><User /></el-icon>
              <span>学生管理</span>
            </el-menu-item>
            <el-menu-item v-if="authStore.isAdmin" index="/students/create">
              <el-icon><Plus /></el-icon>
              <span>添加学生</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header>
          <div class="header">
            <div class="breadcrumb">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item>首页</el-breadcrumb-item>
                <el-breadcrumb-item>学生管理</el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div class="user-info">
              <el-dropdown @command="handleCommand">
                <span class="user-name">
                  {{ authStore.user?.username }}
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>
        
        <!-- 主要内容 -->
        <el-main>
          <div class="student-content">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>学生列表</span>
                  <el-button
                    v-if="authStore.isAdmin"
                    type="primary"
                    @click="$router.push('/students/create')"
                  >
                    <el-icon><Plus /></el-icon>
                    添加学生
                  </el-button>
                </div>
              </template>
              
              <!-- 搜索区域 -->
              <el-form :inline="true" class="search-form">
                <el-form-item label="学号">
                  <el-input v-model="searchForm.student_id" placeholder="请输入学号" clearable />
                </el-form-item>
                <el-form-item label="姓名">
                  <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
                </el-form-item>
                <el-form-item label="班级">
                  <el-input v-model="searchForm.class" placeholder="请输入班级" clearable />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch">
                    <el-icon><Search /></el-icon> 搜索
                  </el-button>
                  <el-button @click="resetSearch">
                    <el-icon><Refresh /></el-icon> 重置
                  </el-button>
                </el-form-item>
              </el-form>
              
              <el-table
                :data="filteredStudents"
                v-loading="studentStore.loading"
                style="width: 100%"
                stripe
                border
                highlight-current-row
              >
                <el-table-column prop="student_id" label="学号" width="120" sortable />
                <el-table-column prop="name" label="姓名" width="120" sortable />
                <el-table-column prop="gender" label="性别" width="80">
                  <template #default="scope">
                    <el-tag :type="scope.row.gender === '男' ? 'primary' : 'success'">
                      {{ scope.row.gender }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="age" label="年龄" width="80" sortable />
                <el-table-column prop="grade" label="年级" width="100" />
                <el-table-column prop="class" label="班级" width="100" />
                <el-table-column prop="created_at" label="创建时间" width="180" sortable>
                  <template #default="scope">
                    {{ formatDate(scope.row.created_at) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="220" fixed="right">
                  <template #default="scope">
                    <el-button
                      type="primary"
                      size="small"
                      @click="viewStudent(scope.row.id)"
                    >
                      <el-icon><View /></el-icon> 查看
                    </el-button>
                    <el-button
                      v-if="authStore.isAdmin"
                      type="warning"
                      size="small"
                      @click="editStudent(scope.row.id)"
                    >
                      <el-icon><Edit /></el-icon> 编辑
                    </el-button>
                    <el-button
                      v-if="authStore.isAdmin"
                      type="danger"
                      size="small"
                      @click="deleteStudent(scope.row)"
                    >
                      <el-icon><Delete /></el-icon> 删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <!-- 分页 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  background
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="totalFilteredStudents"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { House, User, Plus, ArrowDown, Search, Refresh, View, Edit, Delete } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useStudentStore } from '@/stores/student'

const router = useRouter()
const authStore = useAuthStore()
const studentStore = useStudentStore()

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = ref({
  student_id: '',
  name: '',
  class: ''
})

// 过滤后的学生列表
const filteredStudents = computed(() => {
  let result = studentStore.students.filter(student => {
    const matchStudentId = !searchForm.value.student_id || 
      student.student_id.toLowerCase().includes(searchForm.value.student_id.toLowerCase())
    const matchName = !searchForm.value.name || 
      student.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    const matchClass = !searchForm.value.class || 
      student.class.toLowerCase().includes(searchForm.value.class.toLowerCase())
    
    return matchStudentId && matchName && matchClass
  })
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return result.slice(start, end)
})

// 过滤后的总数
const totalFilteredStudents = computed(() => {
  return studentStore.students.filter(student => {
    const matchStudentId = !searchForm.value.student_id || 
      student.student_id.toLowerCase().includes(searchForm.value.student_id.toLowerCase())
    const matchName = !searchForm.value.name || 
      student.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    const matchClass = !searchForm.value.class || 
      student.class.toLowerCase().includes(searchForm.value.class.toLowerCase())
    
    return matchStudentId && matchName && matchClass
  }).length
})

onMounted(async () => {
  authStore.initAuth()
  await fetchStudents()
})

const fetchStudents = async () => {
  try {
    await studentStore.fetchStudents()
  } catch (error) {
    ElMessage.error('获取学生列表失败')
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const resetSearch = () => {
  searchForm.value = {
    student_id: '',
    name: '',
    class: ''
  }
  currentPage.value = 1
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

const viewStudent = (id) => {
  router.push(`/students/${id}`)
}

const editStudent = (id) => {
  router.push(`/students/${id}/edit`)
}

const deleteStudent = async (student) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除学生 ${student.name} (学号: ${student.student_id}) 吗？此操作不可逆！`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    await studentStore.deleteStudent(student.id)
    ElMessage.success(`学生 ${student.name} 已成功删除`)
    
    // 如果当前页没有数据了，则返回上一页
    if (filteredStudents.value.length === 1 && currentPage.value > 1) {
      currentPage.value--
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + (error.message || '未知错误'))
    }
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      authStore.logout()
      ElMessage.success('已退出登录')
      router.push('/login')
    } catch {
      // 用户取消
    }
  }
}
</script>

<style scoped>
.student-list {
  height: 100vh;
}

.sidebar {
  height: 100vh;
  background-color: #304156;
}

.logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #434a50;
}

.logo h3 {
  color: #bfcbd9;
  margin: 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.user-name {
  cursor: pointer;
  color: #606266;
  display: flex;
  align-items: center;
}

.student-content {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>

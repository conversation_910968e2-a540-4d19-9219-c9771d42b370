<template>
  <div class="student-create">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px">
        <div class="sidebar">
          <div class="logo">
            <h3>学生管理系统</h3>
          </div>
          <el-menu
            :default-active="$route.path"
            router
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
          >
            <el-menu-item index="/dashboard">
              <el-icon><House /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="/students">
              <el-icon><User /></el-icon>
              <span>学生管理</span>
            </el-menu-item>
            <el-menu-item index="/students/create">
              <el-icon><Plus /></el-icon>
              <span>添加学生</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header>
          <div class="header">
            <div class="breadcrumb">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item>首页</el-breadcrumb-item>
                <el-breadcrumb-item>学生管理</el-breadcrumb-item>
                <el-breadcrumb-item>添加学生</el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div class="user-info">
              <el-dropdown @command="handleCommand">
                <span class="user-name">
                  {{ authStore.user?.username }}
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>
        
        <!-- 主要内容 -->
        <el-main>
          <div class="student-content">
            <el-card class="form-card">
              <template #header>
                <div class="card-header">
                  <span>添加学生信息</span>
                  <el-button @click="$router.push('/students')">
                    <el-icon><ArrowLeft /></el-icon>
                    返回学生列表
                  </el-button>
                </div>
              </template>
              
              <div class="form-container">
                <el-steps :active="currentStep" finish-status="success" simple class="steps-nav">
                  <el-step title="基本信息" />
                  <el-step title="学籍信息" />
                  <el-step title="确认提交" />
                </el-steps>

                <el-form
                  ref="studentFormRef"
                  :model="studentForm"
                  :rules="studentRules"
                  label-width="100px"
                  class="student-form"
                  :disabled="loading"
                  status-icon
                >
                  <!-- 步骤1: 基本信息 -->
                  <div v-if="currentStep === 0">
                    <el-form-item label="姓名" prop="name">
                      <el-input
                        v-model="studentForm.name"
                        placeholder="请输入学生姓名"
                        clearable
                        maxlength="50"
                        show-word-limit
                        :prefix-icon="User"
                      />
                    </el-form-item>
                    
                    <el-form-item label="性别" prop="gender">
                      <el-radio-group v-model="studentForm.gender">
                        <el-radio label="男">男</el-radio>
                        <el-radio label="女">女</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    
                    <el-form-item label="年龄" prop="age">
                      <el-input-number
                        v-model="studentForm.age"
                        :min="6"
                        :max="30"
                        placeholder="请输入年龄"
                        style="width: 100%"
                        controls-position="right"
                      />
                    </el-form-item>

                    <div class="step-buttons">
                      <el-button type="primary" @click="nextStep">下一步</el-button>
                      <el-button @click="resetForm">重置</el-button>
                    </div>
                  </div>
                  
                  <!-- 步骤2: 学籍信息 -->
                  <div v-if="currentStep === 1">
                    <el-form-item label="学号" prop="student_id">
                      <el-input
                        v-model="studentForm.student_id"
                        placeholder="请输入学号(唯一标识)"
                        clearable
                        maxlength="20"
                        show-word-limit
                        :prefix-icon="Ticket"
                      />
                      <div class="form-tip">* 学号为唯一标识，创建后不可修改</div>
                    </el-form-item>
                    
                    <el-form-item label="年级" prop="grade">
                      <el-select v-model="studentForm.grade" placeholder="请选择年级" style="width: 100%">
                        <el-option v-for="grade in gradeOptions" :key="grade" :label="grade" :value="grade" />
                      </el-select>
                    </el-form-item>
                    
                    <el-form-item label="班级" prop="class">
                      <el-input
                        v-model="studentForm.class"
                        placeholder="请输入班级"
                        clearable
                      />
                    </el-form-item>

                    <div class="step-buttons">
                      <el-button @click="prevStep">上一步</el-button>
                      <el-button type="primary" @click="nextStep">下一步</el-button>
                    </div>
                  </div>

                  <!-- 步骤3: 确认信息 -->
                  <div v-if="currentStep === 2">
                    <el-descriptions title="学生信息确认" :column="1" border>
                      <el-descriptions-item label="学号">{{ studentForm.student_id }}</el-descriptions-item>
                      <el-descriptions-item label="姓名">{{ studentForm.name }}</el-descriptions-item>
                      <el-descriptions-item label="性别">{{ studentForm.gender }}</el-descriptions-item>
                      <el-descriptions-item label="年龄">{{ studentForm.age }}</el-descriptions-item>
                      <el-descriptions-item label="年级">{{ studentForm.grade }}</el-descriptions-item>
                      <el-descriptions-item label="班级">{{ studentForm.class }}</el-descriptions-item>
                    </el-descriptions>

                    <div class="step-buttons">
                      <el-button @click="prevStep">上一步</el-button>
                      <el-button type="primary" :loading="loading" @click="handleSubmit">
                        <el-icon><Check /></el-icon> 提交
                      </el-button>
                    </div>
                  </div>
                </el-form>
              </div>
            </el-card>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { House, User, Plus, ArrowDown, ArrowLeft, Ticket, Check } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useStudentStore } from '@/stores/student'

const router = useRouter()
const authStore = useAuthStore()
const studentStore = useStudentStore()

const studentFormRef = ref()
const loading = ref(false)
const currentStep = ref(0)

// 年级选项
const gradeOptions = [
  '一年级', '二年级', '三年级', '四年级', '五年级', '六年级',
  '初一', '初二', '初三',
  '高一', '高二', '高三',
  '大一', '大二', '大三', '大四'
]

const studentForm = reactive({
  student_id: '',
  name: '',
  gender: '男',
  age: 18,
  grade: '',
  class: ''
})

// 表单验证规则
const studentRules = {
  student_id: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    { min: 5, max: 20, message: '学号长度应为5-20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '学号只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应为2-20个字符', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    { type: 'number', min: 6, max: 30, message: '年龄必须在6-30岁之间', trigger: 'blur' }
  ],
  grade: [
    { required: true, message: '请选择年级', trigger: 'change' }
  ],
  class: [
    { required: true, message: '请输入班级', trigger: 'blur' },
    { min: 1, max: 20, message: '班级长度应为1-20个字符', trigger: 'blur' }
  ]
}

onMounted(() => {
  authStore.initAuth()
})

// 下一步
const nextStep = async () => {
  if (currentStep.value === 0) {
    // 验证第一步的表单
    await studentFormRef.value.validateField(['name', 'gender', 'age'], (valid) => {
      if (valid) {
        currentStep.value++
      }
    })
  } else if (currentStep.value === 1) {
    // 验证第二步的表单
    await studentFormRef.value.validateField(['student_id', 'grade', 'class'], (valid) => {
      if (valid) {
        currentStep.value++
      }
    })
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleSubmit = async () => {
  if (!studentFormRef.value) return
  
  await studentFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const result = await studentStore.createStudent(studentForm)
        ElMessage.success({
          message: '学生创建成功！',
          duration: 2000
        })
        
        // 展示成功消息后跳转到学生列表页
        setTimeout(() => {
          router.push('/students')
        }, 1000)
      } catch (error) {
        ElMessage.error({
          message: error.message || '创建失败，请检查学号是否重复',
          duration: 5000
        })
      } finally {
        loading.value = false
      }
    } else {
      currentStep.value = 0 // 返回第一步进行修改
      ElMessage.warning('表单验证失败，请检查输入信息')
    }
  })
}

const resetForm = () => {
  if (studentFormRef.value) {
    studentFormRef.value.resetFields()
    currentStep.value = 0
  }
}

const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      authStore.logout()
      ElMessage.success('已退出登录')
      router.push('/login')
    } catch {
      // 用户取消
    }
  }
}
</script>

<style scoped>
.student-create {
  height: 100vh;
}

.sidebar {
  height: 100vh;
  background-color: #304156;
}

.logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #434a50;
}

.logo h3 {
  color: #bfcbd9;
  margin: 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.user-name {
  cursor: pointer;
  color: #606266;
  display: flex;
  align-items: center;
}

.student-content {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

.form-container {
  padding: 20px;
}

.steps-nav {
  margin-bottom: 30px;
}

.student-form {
  max-width: 600px;
  margin: 0 auto;
}

.step-buttons {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>

from flask import request
from flask_restful import Resource
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from app.models.user import User
from app.extensions import db

class AuthRegister(Resource):
    def post(self):
        """用户注册"""
        data = request.get_json()
        
        # 验证必需字段
        if not all(k in data for k in ('username', 'password', 'email')):
            return {'message': '缺少必需字段'}, 400
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=data['username']).first():
            return {'message': '用户名已存在'}, 400
        
        # 检查邮箱是否已存在
        if User.query.filter_by(email=data['email']).first():
            return {'message': '邮箱已存在'}, 400
        
        # 创建新用户
        user = User(
            username=data['username'],
            email=data['email'],
            role=data.get('role', 'student')
        )
        user.set_password(data['password'])
        
        try:
            db.session.add(user)
            db.session.commit()
            return {'message': '注册成功', 'user': user.to_dict()}, 201
        except Exception as e:
            db.session.rollback()
            return {'message': '注册失败'}, 500

class AuthLogin(Resource):
    def post(self):
        """用户登录"""
        data = request.get_json()
        
        if not all(k in data for k in ('username', 'password')):
            return {'message': '缺少用户名或密码'}, 400
        
        user = User.query.filter_by(username=data['username']).first()
        
        if user and user.check_password(data['password']):
            access_token = create_access_token(identity=user.id)
            return {
                'message': '登录成功',
                'access_token': access_token,
                'user': user.to_dict()
            }, 200
        
        return {'message': '用户名或密码错误'}, 401

class AuthMe(Resource):
    @jwt_required()
    def get(self):
        """获取当前用户信息"""
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if user:
            return {'user': user.to_dict()}, 200
        
        return {'message': '用户不存在'}, 404
